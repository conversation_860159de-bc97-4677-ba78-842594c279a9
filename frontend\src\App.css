/* Premium Wire Shelves 3D Configurator Styles */

:root {
  /* Dark Mode Color Palette with Green Secondary */
  --premium-primary: #0a0a0a;
  --premium-secondary: #1a1a1a;
  --premium-tertiary: #2a2a2a;
  --premium-accent: #22c55e;
  --premium-accent-light: #4ade80;
  --premium-accent-dark: #16a34a;
  --premium-accent-glow: rgba(34, 197, 94, 0.2);
  --premium-success: #22c55e;
  --premium-success-light: #4ade80;
  --premium-warning: #f59e0b;
  --premium-error: #ef4444;
  --premium-surface: #1a1a1a;
  --premium-surface-elevated: #2a2a2a;
  --premium-surface-muted: #0f0f0f;
  --premium-surface-card: #1f1f1f;
  --premium-text-primary: #ffffff;
  --premium-text-secondary: #d1d5db;
  --premium-text-muted: #9ca3af;
  --premium-text-light: #6b7280;
  --premium-border: #374151;
  --premium-border-light: #4b5563;
  --premium-border-muted: #374151;
  
  /* Dark Mode Shadows */
  --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-premium: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
  --shadow-glow: 0 0 0 1px var(--premium-accent-glow), 0 0 20px rgba(34, 197, 94, 0.3);
  --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.4), 0 4px 16px rgba(0, 0, 0, 0.2);
  --shadow-floating: 0 16px 64px rgba(0, 0, 0, 0.5), 0 8px 32px rgba(0, 0, 0, 0.3);

  /* Dark Mode Gradients */
  --gradient-primary: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  --gradient-accent: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  --gradient-surface: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
  --gradient-card: linear-gradient(145deg, #1f1f1f 0%, #1a1a1a 100%);
  --gradient-header: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(15, 15, 15, 0.95) 100%);
  --gradient-success: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  
  /* Simple Typography System */
  --font-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-mono: 'Courier New', Courier, monospace;
  --font-display: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  /* Enhanced Font Sizes - Better Scaling */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */

  /* Enhanced Spacing System */
  --space-1: 0.25rem;    /* 4px */
  --space-2: 0.5rem;     /* 8px */
  --space-3: 0.75rem;    /* 12px */
  --space-4: 1rem;       /* 16px */
  --space-5: 1.25rem;    /* 20px */
  --space-6: 1.5rem;     /* 24px */
  --space-8: 2rem;       /* 32px */
  --space-10: 2.5rem;    /* 40px */
  --space-12: 3rem;      /* 48px */
  --space-16: 4rem;      /* 64px */
  --space-20: 5rem;      /* 80px */

  /* Premium Animations */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Enhanced Global Styles */
* {
  font-family: var(--font-primary);
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
  color: var(--premium-text-primary);
  background: var(--premium-surface-muted);
  margin: 0;
  padding: 0;
}

/* Dark Mode App Layout */
.premium-app {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #0f0f0f 50%, #1a1a1a 75%, #0a0a0a 100%);
  font-size: var(--text-base);
}

.premium-app-header {
  background: var(--gradient-header);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--premium-border-light);
  box-shadow: var(--shadow-card);
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 50;
}

.premium-app-header .max-w-7xl {
  max-width: none;
  width: 100%;
  padding: var(--space-4) var(--space-6);
}

.premium-app > .max-w-7xl {
  max-width: none;
  width: 100%;
  padding: var(--space-4) var(--space-6);
}

.premium-brand {
  flex-grow: 1;
}

.premium-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--premium-text-primary);
  letter-spacing: -0.025em;
  line-height: 1.2;
  font-family: var(--font-display);
}

.premium-subtitle {
  font-size: var(--text-base);
  color: var(--premium-text-secondary);
  font-weight: 400;
  margin-top: var(--space-1);
  letter-spacing: -0.01em;
  line-height: 1.4;
}

.premium-status-indicators {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.premium-indicator-ai,
.premium-indicator-ready {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--space-4);
  font-weight: 600;
  font-size: var(--text-sm);
  letter-spacing: 0.025em;
  transition: var(--transition-normal);
  white-space: nowrap;
}

.premium-indicator-ai {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-medium);
}

.premium-indicator-ai:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-large);
}

.premium-indicator-ready {
  background: var(--gradient-success);
  color: white;
  box-shadow: var(--shadow-medium);
  animation: premium-pulse 2s infinite;
}

/* Compressed Grid Layout */
.premium-grid {
  display: grid;
  grid-template-columns: 400px 1fr 350px;
  grid-template-rows: auto auto;
  grid-gap: var(--space-6);
  grid-template-areas:
    "chat viewer controls"
    "bom bom bom";
  min-height: 90vh;
  align-items: start;
}

.premium-grid-chat {
  grid-area: chat;
  min-width: 400px;
}

.premium-grid-viewer {
  grid-area: viewer;
  min-width: 600px;
}

.premium-grid-controls {
  grid-area: controls;
  min-width: 350px;
}

.premium-grid-bom {
  grid-area: bom;
  margin-top: var(--space-4);
}

/* Compressed Card Interface */
.premium-card {
  background: var(--gradient-card);
  border-radius: var(--space-4);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--premium-border-light);
  overflow: hidden;
  transition: var(--transition-normal);
  backdrop-filter: blur(20px);
  min-height: 600px;
  position: relative;
}

.premium-card:hover {
  box-shadow: var(--shadow-floating);
  transform: translateY(-2px);
}

.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--premium-accent-light), transparent);
  opacity: 0.6;
}

.premium-header {
  background: var(--gradient-header);
  border-bottom: 1px solid var(--premium-border-light);
  backdrop-filter: blur(20px);
  padding: var(--space-8);
}

/* Enhanced Premium Avatar and Spacing */
.premium-avatar {
  width: var(--space-16);
  height: var(--space-16);
  background: var(--gradient-accent);
  border-radius: var(--space-5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-medium);
  transition: var(--transition-normal);
}

.premium-avatar:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-large);
}

.premium-status-badge {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.25rem;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #bbf7d0;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #059669;
}

.status-dot {
  width: 0.625rem;
  height: 0.625rem;
  background: #10b981;
  border-radius: 50%;
  margin-right: 0.75rem;
}

.premium-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.premium-scrollbar::-webkit-scrollbar-track {
  background: var(--premium-surface-muted);
  border-radius: 4px;
}

.premium-scrollbar::-webkit-scrollbar-thumb {
  background: var(--premium-text-muted);
  border-radius: 4px;
  opacity: 0.5;
}

.premium-scrollbar::-webkit-scrollbar-thumb:hover {
  opacity: 0.8;
}

.premium-user-message {
  background: var(--gradient-accent);
  box-shadow: var(--shadow-medium);
  font-size: var(--text-base);
  padding: var(--space-5) var(--space-6);
  line-height: 1.6;
  border-radius: var(--space-6);
}

.premium-ai-message {
  background: linear-gradient(135deg, var(--premium-surface-card) 0%, var(--premium-surface-muted) 100%);
  border: 1px solid var(--premium-border-light);
  box-shadow: var(--shadow-soft);
  font-size: var(--text-base);
  padding: var(--space-5) var(--space-6);
  line-height: 1.6;
  border-radius: var(--space-6);
}

.premium-loading-dot {
  width: 0.5rem;
  height: 0.5rem;
  background: var(--premium-text-muted);
  border-radius: 50%;
  animation: premium-bounce 1.4s infinite ease-in-out;
}

.premium-input-container {
  background: var(--gradient-header);
  border-top: 1px solid var(--premium-border-light);
  backdrop-filter: blur(20px);
  padding: var(--space-8);
}

.premium-input {
  width: 100%;
  padding: var(--space-5) var(--space-6);
  border: 2px solid var(--premium-border);
  border-radius: var(--space-5);
  background: var(--premium-surface);
  color: var(--premium-text-primary);
  font-size: var(--text-lg);
  font-weight: 500;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-soft);
  line-height: 1.5;
}

.premium-input:focus {
  outline: none;
  border-color: var(--premium-accent);
  box-shadow: var(--shadow-glow);
  transform: translateY(-1px);
}

.premium-input::placeholder {
  color: var(--premium-text-muted);
  font-weight: 400;
}

.premium-send-button {
  padding: var(--space-5) var(--space-8);
  background: var(--gradient-accent);
  color: white;
  border: none;
  border-radius: var(--space-5);
  font-weight: 600;
  font-size: var(--text-base);
  transition: var(--transition-fast);
  box-shadow: var(--shadow-medium);
  cursor: pointer;
  min-width: 120px;
}

.premium-send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-large);
}

.premium-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Premium Parameter Controls - More Spacious */
.premium-ready-badge {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border: 1px solid #a7f3d0;
  border-radius: 1rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--premium-success);
}

.premium-section-required {
  padding: 2rem;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  border-radius: 1.25rem;
  margin-bottom: 1.5rem;
}

.premium-section-optional {
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid var(--premium-border);
  border-radius: 1.25rem;
}

.premium-section-title {
  font-size: 1.375rem;
  font-weight: 700;
  color: var(--premium-text-primary);
  letter-spacing: -0.025em;
  margin-bottom: 1.5rem;
}

.premium-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.premium-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--premium-text-primary);
  letter-spacing: 0.025em;
}

.premium-sublabel {
  font-size: 0.875rem;
  color: var(--premium-text-muted);
  margin-top: 0.375rem;
}

.premium-number-input,
.premium-select {
  padding: 1.125rem 1.5rem;
  border: 2px solid var(--premium-border);
  border-radius: 1rem;
  background: var(--premium-surface);
  color: var(--premium-text-primary);
  font-size: 1.125rem;
  font-weight: 500;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-soft);
}

.premium-number-input:focus,
.premium-select:focus {
  outline: none;
  border-color: var(--premium-accent);
  box-shadow: var(--shadow-glow);
}

.premium-toggle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background: var(--premium-surface);
  border: 1px solid var(--premium-border);
  border-radius: 1rem;
  box-shadow: var(--shadow-soft);
  margin-bottom: 1.5rem;
}

.premium-toggle-info {
  flex-grow: 1;
}

.premium-toggle {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 1.75rem;
}

.premium-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--premium-border);
  transition: var(--transition-normal);
  border-radius: 1.75rem;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.premium-toggle-slider:before {
  position: absolute;
  content: "";
  height: 1.25rem;
  width: 1.25rem;
  left: 0.25rem;
  bottom: 0.25rem;
  background: white;
  transition: var(--transition-normal);
  border-radius: 50%;
  box-shadow: var(--shadow-soft);
}

.premium-toggle input:checked + .premium-toggle-slider {
  background: var(--premium-accent);
}

.premium-toggle input:checked + .premium-toggle-slider:before {
  transform: translateX(1.25rem);
}

/* Premium 3D Viewer */
.premium-3d-container {
  position: relative;
}

.premium-3d-badges {
  display: flex;
  gap: 0.75rem;
}

.premium-style-badge {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--premium-accent-dark);
}

.premium-finish-badge {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--premium-text-secondary);
}

.premium-3d-footer {
  margin-top: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-radius: 0.75rem;
  text-align: center;
}

.premium-3d-footer p {
  font-size: 0.875rem;
  color: var(--premium-text-muted);
  font-weight: 500;
}

.premium-empty-3d {
  min-height: 600px;
  display: flex;
  align-items: center;
}

.premium-empty-state {
  width: 100%;
}

.premium-empty-icon {
  color: var(--premium-text-muted);
  opacity: 0.7;
}

.premium-requirements-card {
  display: inline-block;
  padding: 1.5rem;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  border-radius: 1rem;
  text-align: left;
  margin-top: 1rem;
}

/* Premium Bill of Materials */
.premium-spec-badge {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid var(--premium-border);
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: var(--premium-text-secondary);
}

.premium-table-container {
  overflow-x: auto;
  border-radius: 1rem;
  border: 1px solid var(--premium-border);
  background: var(--premium-surface);
}

.premium-table {
  width: 100%;
  border-collapse: collapse;
}

.premium-table th {
  padding: 1.25rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 2px solid var(--premium-border);
  font-size: 0.875rem;
  font-weight: 700;
  color: var(--premium-text-primary);
  text-align: left;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.premium-table td {
  padding: 1.25rem 1rem;
  border-bottom: 1px solid var(--premium-border-muted);
  font-size: 0.875rem;
  color: var(--premium-text-secondary);
  vertical-align: top;
}

.premium-table tr:hover {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.5) 0%, rgba(241, 245, 249, 0.5) 100%);
}

.premium-model-number {
  font-family: var(--font-mono);
  font-size: 0.75rem;
  padding: 0.5rem;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid var(--premium-border);
  border-radius: 0.5rem;
  color: var(--premium-text-primary);
  font-weight: 600;
}

.premium-category-badge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

/* Premium Animations */
@keyframes premium-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes premium-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes premium-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Enhanced Responsive Design - Better Scaling */
@media (max-width: 1920px) {
  .premium-grid {
    grid-template-columns: 460px 1fr 400px;
    grid-gap: var(--space-8);
  }
}

@media (max-width: 1600px) {
  .premium-grid {
    grid-template-columns: 440px 1fr 380px;
    grid-gap: var(--space-6);
  }

  .premium-app-header .max-w-7xl,
  .premium-app > .max-w-7xl {
    padding: var(--space-6) var(--space-10);
  }

  .premium-title {
    font-size: var(--text-3xl);
  }
}

@media (max-width: 1280px) {
  .premium-grid {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "viewer"
      "chat"
      "controls"
      "bom";
    gap: 2rem;
  }
  
  .premium-grid-chat,
  .premium-grid-viewer,
  .premium-grid-controls {
    min-width: auto;
  }
  
  .premium-title {
    font-size: 2rem;
  }
  
  .premium-status-indicators {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .premium-app-header .max-w-7xl,
  .premium-app > .max-w-7xl {
    padding: 0 1.5rem;
  }
}

@media (max-width: 768px) {
  .premium-app-header .max-w-7xl {
    padding: 1.5rem;
  }
  
  .premium-app > .max-w-7xl {
    padding: 1.5rem;
  }
  
  .premium-title {
    font-size: 1.75rem;
  }
  
  .premium-subtitle {
    font-size: 1rem;
  }
  
  .premium-grid {
    gap: 1.5rem;
  }
  
  .premium-card {
    border-radius: 1.25rem;
    min-height: auto;
  }
  
  .premium-header {
    padding: 1.5rem !important;
  }
  
  .premium-section-required,
  .premium-section-optional {
    padding: 1.5rem;
  }
  
  .premium-input-container {
    padding: 1.5rem;
  }
}

/* Premium Utilities */
.premium-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.premium-transition {
  transition: var(--transition-normal);
}

.premium-shadow-glow {
  box-shadow: var(--shadow-glow);
}

/* Print Styles */
@media print {
  .premium-app {
    background: white !important;
  }
  
  .premium-card {
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
  }
  
  .no-print {
    display: none !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --premium-border: #000000;
    --premium-text-secondary: #000000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode variables can be added here in future versions */
}